import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';

@Processor('videoProcessing')
export class VideoProcessingConsumer extends WorkerHost {
  async process(job: Job<any, any, string>): Promise<any> {
    console.log('Processing video', job.id);
    let progress = 0;
    for (let i = 0; i < 100; i++) {
      await new Promise((resolve) => setTimeout(resolve, 10));
      progress += 1;
      await job.updateProgress(progress);
    }
    return {};
  }
}
