import { DynamicTool } from '@langchain/core/tools';
import { ChatOpenAI } from '@langchain/openai';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { AgentSubgraphService } from './agent-subgraph.service';
import {
  RecipeAgentState,
  ChatAgentState,
  VideoAgentState,
  createStateWithSystemMessage,
} from './state-types';

/**
 * Example tools that can be used with any agent
 */
export const createRecipeTools = (): DynamicTool[] => [
  new DynamicTool({
    name: 'generate_recipe',
    description: 'Generate a recipe based on ingredients and preferences',
    func: async (input: string) => {
      try {
        const parsed = JSON.parse(input);
        const { ingredients, cuisine, difficulty, servings } = parsed;
        
        return JSON.stringify({
          title: `${cuisine || 'Custom'} Recipe with ${Array.isArray(ingredients) ? ingredients.join(', ') : 'various ingredients'}`,
          ingredients: Array.isArray(ingredients) ? ingredients : [],
          instructions: [
            'Prepare all ingredients',
            'Cook according to preference',
            'Serve hot',
          ],
          cookingTime: 30,
          difficulty: (difficulty as string) || 'medium',
          servings: (servings as number) || 4,
        });
      } catch {
        return 'Error: Invalid input format. Please provide a JSON string with ingredients, cuisine, difficulty, and servings.';
      }
    },
  }),
  new DynamicTool({
    name: 'search_ingredients',
    description: 'Search for ingredient information and substitutes',
    func: async (ingredient: string) => {
      return `Information about ${ingredient}: Available in most stores, common substitutes include similar items.`;
    },
  }),
];

export const createChatTools = (): DynamicTool[] => [
  new DynamicTool({
    name: 'search_web',
    description: 'Search the web for information',
    func: async (query: string) => {
      return `Web search results for: ${query}`;
    },
  }),
];

/**
 * Example usage of the AgentSubgraphService with different states
 */
export class AgentExamples {
  constructor(private agentService: AgentSubgraphService) {}

  /**
   * Create a recipe generation agent
   */
  createRecipeAgent(model: ChatOpenAI | ChatGoogleGenerativeAI) {
    const tools = createRecipeTools();
    
    return this.agentService.createAgentSubgraph({
      model,
      tools,
      stateAnnotation: RecipeAgentState,
    });
  }

  /**
   * Create a general chat agent
   */
  createChatAgent(model: ChatOpenAI | ChatGoogleGenerativeAI) {
    const tools = createChatTools();
    
    return this.agentService.createAgentSubgraph({
      model,
      tools,
      stateAnnotation: ChatAgentState,
    });
  }

  /**
   * Create a video analysis agent
   */
  createVideoAgent(model: ChatOpenAI | ChatGoogleGenerativeAI) {
    const tools = [
      new DynamicTool({
        name: 'analyze_video',
        description: 'Analyze video content',
        func: async (input: string) => {
          try {
            const parsed = JSON.parse(input);
            const { url, analysisType } = parsed;
            return `Video analysis (${analysisType as string}) for ${url as string}: Analysis complete.`;
          } catch {
            return 'Error: Invalid input format. Please provide a JSON string with url and analysisType.';
          }
        },
      }),
    ];

    return this.agentService.createAgentSubgraph({
      model,
      tools,
      stateAnnotation: VideoAgentState,
    });
  }

  /**
   * Create an agent with a custom system message
   */
  createCustomAgent(
    model: ChatOpenAI | ChatGoogleGenerativeAI,
    systemPrompt: string,
    tools: DynamicTool[] = [],
  ) {
    const customState = createStateWithSystemMessage(systemPrompt);
    
    return this.agentService.createAgentSubgraph({
      model,
      tools,
      stateAnnotation: customState,
    });
  }
}

/**
 * Example of how to use the agents in a NestJS service
 */
export async function exampleUsage() {
  const agentService = new AgentSubgraphService();
  const examples = new AgentExamples(agentService);

  // Create models
  const openaiModel = new ChatOpenAI({
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
  });

  // Create a recipe agent
  const recipeAgent = examples.createRecipeAgent(openaiModel);
  
  // Example of running an agent
  const recipeResult = await recipeAgent.invoke({
    messages: [],
    input: {
      ingredients: ['chicken', 'rice', 'vegetables'],
      cuisine: 'Asian',
      difficulty: 'medium',
    },
    output: undefined,
  });

  console.log('Recipe result:', recipeResult);
  
  return recipeResult;
}
