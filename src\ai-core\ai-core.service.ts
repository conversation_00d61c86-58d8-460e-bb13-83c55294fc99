import {
  StateGraph,
  Annotation,
  AnnotationRoot,
  START,
  END,
  MessagesAnnotation,
} from '@langchain/langgraph';
import { BaseMessage } from '@langchain/core/messages';
import { ToolNode } from '@langchain/langgraph/prebuilt';
import { BaseTool } from '@langchain/core/tools';
import { ChatOpenAI } from '@langchain/openai';
import { StateDefinition } from '@langchain/langgraph';

type StateWithMessages = {
  messages: BaseMessage[];
  [key: string]: any;
};

// Type constraint for states that must contain at least a messages field
type GraphStateWithMessages<T extends Record<string, any>> = T & {
  messages: BaseMessage[];
};

type HasMessagesField = {
  messages: BaseMessage[];
};

// Type for the Annotation.Root that includes messages
type AnnotationRootWithMessages<SD extends Record<string, any>> =
  AnnotationRoot<SD> & {
    State: GraphStateWithMessages<any>;
  };

// Generic function to create a React agent subgraph
export function createReactSubgraph<
  SD extends StateDefinition & HasMessagesField,
>(
  state: AnnotationRoot<SD>,
  tools: BaseTool[],
  model?: ChatOpenAI,
  config?: {
    agentNodeName?: string;
    toolsNodeName?: string;
    maxIterations?: number;
  },
): StateGraph<typeof state.State, typeof state.Update> {
  const {
    agentNodeName = 'agent',
    toolsNodeName = 'tools',
    maxIterations = 10,
  } = config || {};

  // Create the graph with the passed state definition
  const workflow = new StateGraph(state);

  // Initialize the model with tools
  const llm = (
    model ||
    new ChatOpenAI({
      modelName: 'gpt-4',
      temperature: 0,
    })
  ).bindTools(tools);

  // Define the agent node
  const agentNode = async (state: StateWithMessages) => {
    const { messages } = state;

    // Check iteration count
    const toolCalls = messages.filter(
      (msg) => msg.additional_kwargs?.tool_calls?.length > 0,
    );

    if (toolCalls.length >= maxIterations) {
      return {
        messages: [
          {
            role: 'assistant',
            content: 'Reached maximum iterations. Stopping execution.',
          },
        ],
      };
    }

    // Call the model
    const response = await llm.invoke(messages);

    return {
      messages: [response],
    };
  };

  // Create tool node
  const toolNode = new ToolNode(tools);

  // Add nodes to the graph
  workflow.addNode(agentNodeName, agentNode);
  workflow.addNode(toolsNodeName, toolNode);

  // Define the conditional edge
  const shouldContinue = (state: typeof state.State) => {
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];

    // Check if the last message has tool calls
    if (
      lastMessage.additional_kwargs?.tool_calls &&
      lastMessage.additional_kwargs.tool_calls.length > 0
    ) {
      return toolsNodeName;
    }

    return END;
  };

  // Set up edges
  workflow.addEdge(START, agentNodeName);
  workflow.addConditionalEdges(agentNodeName, shouldContinue, {
    [toolsNodeName]: toolsNodeName,
    [END]: END,
  });
  workflow.addEdge(toolsNodeName, agentNodeName);

  return workflow;
}
