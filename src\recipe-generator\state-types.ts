import { BaseMessage, SystemMessage } from '@langchain/core/messages';
import { Annotation } from '@langchain/langgraph';

/**
 * Base message annotation that can be reused across different states
 * This ensures all states have the required messages field with proper reducer
 */
export const MessagesAnnotation = Annotation<BaseMessage[]>({
  reducer: (x, y) => x.concat(y),
  default: () => [],
});

/**
 * Recipe generation specific state
 */
export interface RecipeInput {
  ingredients: string[];
  cuisine?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  servings?: number;
}

export interface RecipeOutput {
  title: string;
  ingredients: string[];
  instructions: string[];
  cookingTime: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

export const RecipeAgentState = Annotation.Root({
  messages: MessagesAnnotation,
  input: Annotation<RecipeInput>(),
  output: Annotation<RecipeOutput>(),
});

/**
 * Generic chat state for simple conversations
 */
export const ChatAgentState = Annotation.Root({
  messages: MessagesAnnotation,
  context: Annotation<string>(),
});

/**
 * Video analysis state (as shown in your example)
 */
export interface VideoInfo {
  title: string;
  duration: number;
  description: string;
}

export interface VideoInput {
  url: string;
  analysisType: 'summary' | 'transcript' | 'metadata';
}

export interface VideoOutput {
  summary?: string;
  transcript?: string;
  metadata?: Record<string, any>;
}

export const VideoAgentState = Annotation.Root({
  messages: MessagesAnnotation,
  input: Annotation<VideoInput>(),
  videoInfo: Annotation<VideoInfo>(),
  output: Annotation<VideoOutput>(),
});

/**
 * Type helper to ensure any state extends the required shape
 */
export type ValidAgentState<T> = T extends {
  State: { messages: BaseMessage[] };
}
  ? T
  : never;

/**
 * Example of how to create a custom state with system message
 */
export function createStateWithSystemMessage(systemPrompt: string) {
  return Annotation.Root({
    messages: Annotation<BaseMessage[]>({
      reducer: (x, y) => x.concat(y),
      default: () => [new SystemMessage(systemPrompt)],
    }),
  });
}
