# General-Purpose Agent Subgraph System

This system provides a reusable way to create LangGraph agent subgraphs that can work with any state type, as long as it contains a `messages` field with the correct type and reducer.

## Core Components

### 1. AgentSubgraphService

The main service that creates agent subgraphs. It provides:

- `shouldContinue()`: Determines whether to continue with tool calls or end
- `callModel()`: Processes state and generates AI responses  
- `createAgentSubgraph()`: Creates a complete agent workflow

### 2. State Types

Reusable state definitions that all include the required `messages` field:

- `MessagesAnnotation`: Base message annotation with proper reducer
- `RecipeAgentState`: For recipe generation tasks
- `ChatAgentState`: For general conversations
- `VideoAgentState`: For video analysis tasks
- `createStateWithSystemMessage()`: Helper to create custom states

### 3. Usage Examples

The `AgentExamples` class shows how to create different types of agents:

- Recipe generation agent
- Chat agent  
- Video analysis agent
- Custom agent with system prompts

## Key Design Principles

### Required State Shape

Any state used with the system must have a `messages` field:

```typescript
interface RequiredStateShape {
  messages: BaseMessage[];
}
```

### General-Purpose Design

The `createAgentSubgraph` function is designed to work with any state type that extends the required shape:

```typescript
createAgentSubgraph<S extends StateType<StateDefinition> & RequiredStateShape>({
  model: ChatOpenAI | ChatGoogleGenerativeAI,
  tools: DynamicTool[],
  stateAnnotation: any, // The Annotation.Root result
})
```

### Flexible Messages Annotation

The `MessagesAnnotation` provides a standard way to handle messages across all states:

```typescript
export const MessagesAnnotation = Annotation<BaseMessage[]>({
  reducer: (x, y) => x.concat(y),
  default: () => [],
});
```

## Usage Examples

### Creating a Recipe Agent

```typescript
const agentService = new AgentSubgraphService();
const examples = new AgentExamples(agentService);

const model = new ChatOpenAI({
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
});

const recipeAgent = examples.createRecipeAgent(model);

// Run the agent
const result = await recipeAgent.invoke({
  messages: [],
  input: {
    ingredients: ['chicken', 'rice', 'vegetables'],
    cuisine: 'Asian',
    difficulty: 'medium',
  },
  output: undefined,
});
```

### Creating a Custom Agent

```typescript
// Define your own state
const CustomState = Annotation.Root({
  messages: MessagesAnnotation,
  customField: Annotation<string>(),
});

// Create custom tools
const customTools = [
  new DynamicTool({
    name: 'custom_tool',
    description: 'Custom functionality',
    func: async (input: string) => {
      return `Processed: ${input}`;
    },
  }),
];

// Create the agent
const customAgent = agentService.createAgentSubgraph({
  model,
  tools: customTools,
  stateAnnotation: CustomState,
});
```

### Using with System Messages

```typescript
const customState = createStateWithSystemMessage(
  'You are a helpful assistant specialized in coding questions.'
);

const agent = agentService.createAgentSubgraph({
  model,
  tools: chatTools,
  stateAnnotation: customState,
});
```

## Benefits

1. **Reusability**: One service works for all agent types
2. **Type Safety**: Ensures all states have required message field
3. **Flexibility**: Easy to create new agent types
4. **Consistency**: Standard pattern for all agents
5. **Modularity**: Clean separation of concerns

## File Structure

```
src/recipe-generator/
├── agent-subgraph.service.ts  # Core service
├── state-types.ts             # Reusable state definitions
├── usage-examples.ts          # Example implementations
├── recipe-generator.module.ts # NestJS module
├── recipe-generator.service.ts # Recipe-specific service
└── recipe-generator.controller.ts # HTTP endpoints
```

This system allows you to easily create new agent types while maintaining consistency and reusability across your application.
