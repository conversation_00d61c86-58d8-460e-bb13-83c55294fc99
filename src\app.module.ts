import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { BullModule } from '@nestjs/bullmq';
import { VideoProcessingConsumer } from './app.processor';

@Module({
  imports: [
    BullModule.forRoot({
      connection: {
        host: process.env.REDIS_HOST ?? 'redis',
        port: parseInt(process.env.REDIS_PORT ?? '6379'),
      },
    }),
    BullModule.registerQueue({
      name: 'videoProcessing',
    }),
  ],
  controllers: [AppController],
  providers: [AppService, VideoProcessingConsumer],
})
export class AppModule {}
