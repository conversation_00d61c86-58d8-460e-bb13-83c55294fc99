import { Injectable } from '@nestjs/common';
import z from 'zod';

interface Input {
  url: string;
  Language?: string;
}

interface VideoInfo {
  videoPath: string;
  videoDescription: string;
}

type Output = z.infer<typeof RecipeSchema>;

const systemPrompt = `
You are an expert culinary assistant.
You will receive a cooking video (e.g., TikTok) and the video description.
Extract a clean, structured recipe from both the video content and the video description, as ingredients and steps may be mentioned in either source.
If there is insufficient info, make minimal reasonable assumptions but mark unknowns as null.

You must respond with valid XML following this exact schema:

<recipe>
  <title>Recipe title (1-200 characters)</title>
  <category>Recipe category (optional)</category>
  <description>Recipe description (optional)</description>
  <cooking_time_minutes>Cooking time in minutes (number or null)</cooking_time_minutes>
  <servings>Number of servings (number or null)</servings>
  <ingredients>
    <ingredient>
      <quantity>Numeric quantity (positive number)</quantity>
      <unit>Unit of measurement (string)</unit>
      <name>Ingredient name (string)</name>
    </ingredient>
    <!-- Repeat for each ingredient -->
  </ingredients>
  <steps>
    <step>
      <name>Step name (string)</name>
      <description>Step description (string)</description>
    </step>
    <!-- Repeat for each step -->
  </steps>
  <metadata>
    <audio_helpful>true/false (optional)</audio_helpful>
    <video_type>cinematic/vlog/tutorial/other (optional)</video_type>
  </metadata>
</recipe>

Output requirements:
- Keep measurements and quantities if mentioned
- Keep the order of steps as presented
- Include time and servings only if clearly stated
- Use concise, clear language
- Return ONLY the XML, no additional text or explanations
`;

const IngredientSchema = z.object({
  quantity: z.number().positive(),
  unit: z.string().min(1),
  name: z.string().min(1),
});

const StepSchema = z.object({
  name: z.string().min(1),
  description: z.string().min(1),
});

//TODO improve metadata
const MetadataSchema = z.object({
  audio_helpful: z.boolean().optional(),
  video_type: z.enum(['cinematic', 'vlog', 'tutorial', 'other']).optional(),
});

const RecipeSchema = z.object({
  title: z.string().min(1).max(200),
  category: z.string().min(1).optional(),
  description: z.string().min(1).optional(),
  cooking_time_minutes: z.number().int().positive().nullable().optional(),
  servings: z.number().int().positive().nullable().optional(),
  ingredients: z.array(IngredientSchema).default([]),
  steps: z.array(StepSchema).default([]),
  metadata: MetadataSchema.optional(),
});


@Injectable()
export class RecipeGeneratorService {
  constructor() {}

  recipeGenerator() {}
}
